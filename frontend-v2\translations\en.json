{"meta": {"title": "Puneeth Y | Cloud Resume", "description": "Puneeth Y's Cloud Resume Challenge – DevOps, Cloud, AWS, Kubernetes, and creative portfolio. Explore my journey, skills, projects, and achievements.", "keywords": "Puneeth Y, Cloud Resume, DevOps, AWS, Kubernetes, Portfolio, Open Source, Engineer, Blog, Photography", "author": "Puneeth Y", "ogTitle": "Puneeth Y | Cloud Resume | DevOps Engineer & Cloud Enthusiast", "ogDescription": "Puneeth Y's Cloud Resume Challenge – DevOps, Cloud, AWS, Kubernetes, and creative portfolio.", "twitterTitle": "Puneeth Y | Cloud Resume | DevOps Engineer & Cloud Enthusiast", "twitterDescription": "Puneeth Y's Cloud Resume Challenge – DevOps, Cloud, AWS, Kubernetes, and creative portfolio."}, "navigation": {"home": "Home", "about": "About", "pipeline": "Pipeline", "skills": "Skills", "projects": "Projects", "experience": "Experience", "blogs": "Blogs", "photography": "Photography"}, "hero": {"name": "Puneeth Y", "subtitle": "An Aspiring DevOps Engineer & Cloud Architect crafting resilient, automated, and scalable infrastructure for tomorrow's technology."}, "about": {"title": "About Me", "intro": "<PERSON><PERSON>, I'm an aspiring DevOps engineer with a passion for automation and system orchestration.", "description": "I love building efficient and reliable infrastructure using tools like Docker, Kubernetes, and AWS. Whether it's writing Infrastructure as Code, managing containers, or setting up CI/CD pipelines, I'm all about creating systems that make life easier for developers, helping them ship faster, safer, and with confidence.", "hobby": "Outside of tech, I enjoy photography and often spend time exploring nature with my camera, capturing moments when I can."}, "stats": {"experience": "Years of Experience", "visitors": "Visitors", "poweredBy": "Powered by AWS Lambda", "commitment": "Commitment to Automation"}, "skills": {"title": "My skills", "description": "I have hands-on experience with HTML, CSS, JavaScript, Python, and Golang. I'm also familiar with frameworks and libraries such as React, .NET, Django, and Node.js, often integrating them in full-stack development projects. On the data side, I've worked with both relational and non-relational databases, including MSSQL and MongoDB. I've also gained practical experience with AWS services, particularly for deploying and scaling cloud-native applications. My infrastructure knowledge includes working with Kubernetes, Helm, Terraform, Prometheus, and Grafana to manage, deploy, and monitor applications efficiently. Curious for more? Feel free to check out my CV along the way!", "infrastructure": "My infrastructure knowledge includes working with Kubernetes, Helm, Terraform, Prometheus, and Grafana to manage, deploy, and monitor applications efficiently.", "curious": "Curious for more? Feel free to check out my CV along the way!", "viewCV": "View CV"}, "pipeline": {"code": "Code", "build": "Build", "test": "Test", "deploy": "Deploy"}, "portfolio": {"title": "Featured Projects", "description": "A selection of my recent software and DevOps projects. Hover over each card to see more details.", "portfolioProject0title": "CODESOURCERER", "portfolioProject1title": "Vitista", "portfolioProject2title": "Sputilties", "portfolioProject3title": "<PERSON><PERSON>", "portfolioProject0description": "A tool that automates test suite generation for code changes using a Gemini-powered proxy server, completely integrates with GitHub to create filtered tests via pull requests.", "portfolioProject1description": "A comprehensive personal healthcare app designed to empower individuals on their journey to optimal well-being.", "portfolioProject2description": "A collection of Spotify utilities and tools to enhance your music streaming experience.", "portfolioProject3description": "A collaborative meeting and team communication platform for remote teams."}, "certifications": {"title": "Awards & Certifications", "description": "A collection of certifications and recognitions I've received along my learning and DevOps journey.", "items0": "Top 1% in GSSoC'ext 2024", "items1": "1st Place in the department-level mini-project", "items2": "AZ-305 Microsoft Azure Architect Design Prerequisites", "items3": "AWS Cloud Practitioner Essentials", "items4": "Postman API Fundamentals Student Expert"}, "experience": {"title": "Work Experience", "description": "My professional journey in the world of DevOps and cloud engineering.", "jobs0Company": "HCLSoftware", "jobs0position": "Intern", "jobs0period": "Mar 2025 - Present", "jobs0description": "Contributed as a primary team member in architecting the upcoming cloud-native migration blueprint, focusing on defining the target architecture, technology stack, and deployment patterns tailored for future scalability and maintainability. Led several targeted proofs of concept (PoCs) to validate new tools, frameworks, and deployment strategies, directly influencing final architecture decisions and shaping the release automation framework later adopted in the company's product.", "jobs1company": "GirlScript Summer of Code (GSSoC-ext'24)", "jobs1position": "Open Source Contributor", "jobs1period": "Oct 2024 - Nov 2024", "jobs1description": "Actively contributed to open-source repositories as part of GSSoC-ext 2024, demonstrating strong coding proficiency, collaboration, and problem-solving abilities. Achieved a top ranking of 281 out of 60,000 participants, reflecting high technical competence and consistent engagement throughout the program."}, "blog": {"title": "My Blog", "description": "Sharing my journey and insights in cloud computing and DevOps.", "readBlog": "Read The Blog", "posts0title": "My Attempt at the AWS Cloud Resume Challenge", "posts0excerpt": "Curious about how the architecture behind this website is structured using modern DevOps practices? I break it all down in my blog post as part of the Cloud Resume Challenge.\n\nFrom CI/CD to infrastructure as code, it's all in there. Be sure to check it out!"}, "photography": {"title": "Photography Portfolio", "description": "Beyond the code, I enjoy capturing the world through my lens. Here's a glimpse of my creative side.", "viewFull": "View Full Photography Portfolio"}, "footer": {"builtWith": "Built as part of the AWS Cloud Resume Challenge.", "copyright": "© 2025 Puneeth Y. All Rights Reserved."}, "toast": {"welcome": "Welcome!", "thanks": "Thanks for visiting my portfolio.", "visitor": "You are visitor #"}, "ui": {"close": "×"}}